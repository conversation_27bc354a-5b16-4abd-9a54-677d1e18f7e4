# 长进程名问题修复说明

## 问题描述

当目标进程名称较长时（如`DeltaForceClient-Win64-Shipping.exe`，37个字符），无法正常获取模块基址。

## 根本原因

### 1. EPROCESS.ImageFileName字段限制
Windows内核中的EPROCESS结构体的ImageFileName字段只有**15个字符**（加上null终止符共16字节）。

```cpp
char name[16] = { };  // 只能存储15个字符
read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
```

对于长进程名`DeltaForceClient-Win64-Shipping.exe`：
- 完整进程名：37个字符
- ImageFileName只能存储：`DeltaForceClien`（前15个字符）
- 导致字符串匹配失败

### 2. 原始匹配逻辑的问题
```cpp
// 原始代码 - 有问题的匹配逻辑
if (strstr(image_name, name) && process_id == get_process_id(image_name))
```

这种逻辑要求：
1. 完整进程名包含ImageFileName（截断的名称）
2. 同时PID匹配

但对于长进程名，ImageFileName被截断，可能导致误匹配。

## 修复方案

### 1. 改进进程匹配逻辑
```cpp
// 修复后的代码
char name[16] = { };
read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
name[15] = '\0';  // 确保null终止

int process_id = 0;
read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

// 优先使用PID匹配而不是依赖ImageFileName
uintptr_t target_pid = get_process_id(image_name);
if (target_pid == 0) {
    continue;  // 进程不存在，跳过
}

// 优先使用PID匹配，ImageFileName作为辅助验证
bool pid_match = (process_id == target_pid);
bool name_match = (strlen(name) > 0 && strstr(image_name, name) != nullptr);

// 对于长进程名，主要依赖PID匹配
if (pid_match && (name_match || strlen(image_name) > 15))
```

### 2. 匹配策略改进
- **主要依赖PID匹配**：PID是唯一标识符，更可靠
- **ImageFileName作为辅助验证**：仅在进程名较短时使用
- **长进程名特殊处理**：当进程名超过15个字符时，主要使用PID匹配

## 测试验证

### 测试用例
1. **短进程名**（≤15字符）：如`notepad.exe`
   - 使用PID + ImageFileName双重验证
   
2. **长进程名**（>15字符）：如`DeltaForceClient-Win64-Shipping.exe`
   - 主要使用PID匹配
   - ImageFileName仅作为前缀验证

### 调试信息增强
```cpp
printf("[*] 进程名长度: %zu 字符\n", strlen(target_process));

// 先检查进程是否存在
uintptr_t pid = driver.get_process_id(target_process);
if (pid == 0) {
    printf("[-] 进程 %s 未运行\n", target_process);
    return 1;
}
printf("[+] 找到进程 PID: %lu\n", (unsigned long)pid);
```

## 技术细节

### EPROCESS结构体限制
```
EPROCESS.ImageFileName:
- 大小：16字节（包含null终止符）
- 有效字符：15个
- 存储内容：进程可执行文件名（不含路径）
- 限制：会截断长文件名
```

### 进程名截断示例
```
原始进程名: DeltaForceClient-Win64-Shipping.exe (37字符)
ImageFileName: DeltaForceClien (15字符，被截断)
```

### PID匹配的优势
1. **唯一性**：PID在系统中是唯一的
2. **完整性**：不会被截断
3. **可靠性**：直接从进程快照获取，准确度高

## 兼容性说明

此修复保持向后兼容：
- 短进程名：继续使用双重验证（PID + ImageFileName）
- 长进程名：自动切换到PID主导的匹配策略
- 不影响现有功能的正常使用

## 建议的进一步改进

1. **添加进程路径匹配**：通过PEB获取完整的可执行文件路径
2. **缓存PID映射**：避免重复调用get_process_id
3. **错误日志增强**：添加更详细的匹配失败原因
4. **性能优化**：减少内核内存读取次数

## 使用示例

```cpp
// 现在可以正确处理长进程名
const char* long_process = "DeltaForceClient-Win64-Shipping.exe";
uintptr_t base = driver.get_process_base(long_process);
if (base) {
    printf("[+] 成功获取进程基址: 0x%llx\n", base);
    
    // 获取模块基址也能正常工作
    uintptr_t kernel32 = driver.get_module_base(long_process, "kernel32.dll");
    printf("[+] kernel32.dll 基址: 0x%llx\n", kernel32);
}
```
