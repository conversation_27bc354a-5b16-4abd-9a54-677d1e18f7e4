#include <Windows.h>
#include <map>

#include "drv.h"

int main()
{
	printf("=== 驱动开发：内核取应用层模块基地址 ===\n");
	printf("基于LyShark博客文章实现的64位版本\n\n");

	eneio_lib driver;

	// 测试获取进程基址
	const char* target_process = "DeltaForceClient-Win64-Shipping.exe";
	printf("[*] 正在获取进程 %s 的基址...\n", target_process);
	printf("[*] 进程名长度: %zu 字符\n", strlen(target_process));

	// 先检查进程是否存在
	uintptr_t pid = driver.get_process_id(target_process);
	if (pid == 0) {
		printf("[-] 进程 %s 未运行\n", target_process);
		system("pause");
		return 1;
	}
	printf("[+] 找到进程 PID: %lu\n", (unsigned long)pid);

	uintptr_t base = driver.get_process_base(target_process);

	if (!base)
	{
		printf("[-] 进程 %s 未运行或获取失败\n", target_process);
		system("pause");
		return 1;  // 修复：返回int而不是bool
	}

	printf("[+] %s 基址: 0x%llx\n", target_process, base);

	// 验证进程基址（读取PE头）
	UINT8 pe_header[2] = { 0 };
	if (driver.read_virtual_memory(base, pe_header, 2))
	{
		if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) // MZ header
		{
			printf("[+] PE头验证成功 (MZ: 0x%02x 0x%02x)\n", pe_header[0], pe_header[1]);
		}
		else
		{
			printf("[-] PE头验证失败\n");
		}
	}
	else
	{
		printf("[-] 无法读取进程内存\n");
	}

	printf("\n[*] 正在获取模块基址...\n");

	// 测试获取常见模块基址
	const char* modules[] = {
		"kernel32.dll",
		"ntdll.dll",
		"user32.dll",
		"advapi32.dll"
	};

	for (int i = 0; i < sizeof(modules) / sizeof(modules[0]); i++)
	{
		uintptr_t module_base = driver.get_module_base(target_process, modules[i]);
		if (module_base)
		{
			printf("[+] %s 基址: 0x%llx\n", modules[i], module_base);

			// 验证模块基址（读取PE头）
			UINT8 module_pe[2] = { 0 };
			if (driver.read_virtual_memory(module_base, module_pe, 2))
			{
				if (module_pe[0] == 0x4D && module_pe[1] == 0x5A)
				{
					printf("    └─ PE头验证成功\n");
				}
				else
				{
					printf("    └─ PE头验证失败\n");
				}
			}
			else
			{
				printf("    └─ 无法读取模块内存\n");
			}
		}
		else
		{
			printf("[-] 获取 %s 基址失败\n", modules[i]);
		}
	}

	printf("\n[+] 模块基址获取功能测试完成！\n");
	printf("按任意键退出...\n");
	system("pause");

	return 0;  // 修复：成功时返回0
}